// DOM Elements
const logoutBtn = document.getElementById('logoutBtn');
const userNameSpan = document.getElementById('userName');
const userEmailSpan = document.getElementById('userEmail');
const loginTimeSpan = document.getElementById('loginTime');

// Check if user is logged in
function checkAuth() {
    const isLoggedIn = sessionStorage.getItem('isLoggedIn');
    const userEmail = sessionStorage.getItem('userEmail');
    
    if (!isLoggedIn || !userEmail) {
        // Redirect to login page if not logged in
        window.location.href = 'login.html';
        return;
    }
    
    // Display user information
    displayUserInfo(userEmail);
}

// Display user information
function displayUserInfo(email) {
    // Set user email
    userEmailSpan.textContent = email;
    
    // Set user name (extract from email or use stored name)
    const userName = sessionStorage.getItem('userName') || email.split('@')[0];
    userNameSpan.textContent = userName;
    
    // Set login time
    const loginTime = sessionStorage.getItem('loginTime');
    if (loginTime) {
        const date = new Date(loginTime);
        loginTimeSpan.textContent = date.toLocaleString('vi-VN');
    } else {
        loginTimeSpan.textContent = new Date().toLocaleString('vi-VN');
    }
}

// Logout functionality
logoutBtn.addEventListener('click', function() {
    // Show confirmation dialog
    if (confirm('Bạn có chắc chắn muốn đăng xuất?')) {
        // Clear session data
        sessionStorage.removeItem('isLoggedIn');
        sessionStorage.removeItem('userEmail');
        sessionStorage.removeItem('userName');
        sessionStorage.removeItem('loginTime');
        
        // Show logout message
        showLogoutMessage();
        
        // Redirect to login page after a short delay
        setTimeout(() => {
            window.location.href = 'login.html';
        }, 1500);
    }
});

// Show logout message
function showLogoutMessage() {
    const logoutDiv = document.createElement('div');
    logoutDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #e74c3c;
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 1000;
        animation: slideInRight 0.3s ease-out;
    `;
    logoutDiv.textContent = 'Đăng xuất thành công!';
    
    document.body.appendChild(logoutDiv);
    
    setTimeout(() => {
        logoutDiv.remove();
    }, 1500);
}

// Action buttons functionality
document.addEventListener('DOMContentLoaded', function() {
    // Check authentication first
    checkAuth();
    
    // Add click handlers for action buttons
    const actionButtons = document.querySelectorAll('.action-btn');
    actionButtons.forEach((btn, index) => {
        btn.addEventListener('click', function() {
            const actions = ['Tạo mới', 'Báo cáo', 'Cài đặt', 'Hồ sơ'];
            const actionName = actions[index] || 'Thao tác';
            
            // Show demo message
            showActionMessage(`Đã nhấn: ${actionName}`);
        });
    });
    
    // Add hover effects to stat cards
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
    
    // Add click effects to activity items
    const activityItems = document.querySelectorAll('.activity-item');
    activityItems.forEach(item => {
        item.addEventListener('click', function() {
            showActionMessage('Xem chi tiết hoạt động');
        });
    });
});

// Show action message
function showActionMessage(message) {
    const messageDiv = document.createElement('div');
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #3498db;
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 1000;
        animation: slideInRight 0.3s ease-out;
    `;
    messageDiv.textContent = message;
    
    // Add animation keyframes if not exists
    if (!document.querySelector('#actionAnimation')) {
        const style = document.createElement('style');
        style.id = 'actionAnimation';
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `;
        document.head.appendChild(style);
    }
    
    document.body.appendChild(messageDiv);
    
    setTimeout(() => {
        messageDiv.remove();
    }, 2000);
}

// Auto-refresh stats (demo)
function updateStats() {
    const statNumbers = document.querySelectorAll('.stat-number');
    statNumbers.forEach(stat => {
        const currentValue = parseInt(stat.textContent.replace(/[^\d]/g, ''));
        if (!isNaN(currentValue)) {
            // Random small change for demo
            const change = Math.floor(Math.random() * 10) - 5;
            const newValue = Math.max(0, currentValue + change);
            
            // Animate number change
            animateNumber(stat, currentValue, newValue);
        }
    });
}

// Animate number changes
function animateNumber(element, start, end) {
    const duration = 1000;
    const startTime = performance.now();
    const suffix = element.textContent.replace(/[\d,]/g, '');
    
    function update(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        const current = Math.floor(start + (end - start) * progress);
        element.textContent = current.toLocaleString() + suffix;
        
        if (progress < 1) {
            requestAnimationFrame(update);
        }
    }
    
    requestAnimationFrame(update);
}

// Update stats every 30 seconds (demo)
setInterval(updateStats, 30000);

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + L for logout
    if ((e.ctrlKey || e.metaKey) && e.key === 'l') {
        e.preventDefault();
        logoutBtn.click();
    }
    
    // Escape key to show help
    if (e.key === 'Escape') {
        showActionMessage('Phím tắt: Ctrl+L để đăng xuất');
    }
});

// Page visibility change handler
document.addEventListener('visibilitychange', function() {
    if (document.visibilityState === 'visible') {
        // Check auth when page becomes visible again
        checkAuth();
    }
});
