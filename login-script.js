// DOM Elements
const loginForm = document.getElementById('loginForm');
const emailInput = document.getElementById('email');
const passwordInput = document.getElementById('password');
const togglePasswordBtn = document.getElementById('togglePassword');
const loginBtn = document.getElementById('loginBtn');
const loadingSpinner = document.getElementById('loadingSpinner');
const emailError = document.getElementById('emailError');
const passwordError = document.getElementById('passwordError');
const rememberMeCheckbox = document.getElementById('rememberMe');

// Toggle password visibility
togglePasswordBtn.addEventListener('click', function() {
    const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
    passwordInput.setAttribute('type', type);
    
    // Change eye icon
    const eyeIcon = this.querySelector('.eye-icon');
    eyeIcon.textContent = type === 'password' ? '👁️' : '🙈';
});

// Email validation
function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Password validation
function validatePassword(password) {
    return password.length >= 6;
}

// Show error message
function showError(element, message) {
    element.textContent = message;
    element.style.display = 'block';
}

// Hide error message
function hideError(element) {
    element.textContent = '';
    element.style.display = 'none';
}

// Real-time validation
emailInput.addEventListener('blur', function() {
    const email = this.value.trim();
    if (email && !validateEmail(email)) {
        showError(emailError, 'Vui lòng nhập email hợp lệ');
        this.style.borderColor = '#e74c3c';
    } else {
        hideError(emailError);
        this.style.borderColor = '#e1e5e9';
    }
});

passwordInput.addEventListener('blur', function() {
    const password = this.value;
    if (password && !validatePassword(password)) {
        showError(passwordError, 'Mật khẩu phải có ít nhất 6 ký tự');
        this.style.borderColor = '#e74c3c';
    } else {
        hideError(passwordError);
        this.style.borderColor = '#e1e5e9';
    }
});

// Clear errors on input
emailInput.addEventListener('input', function() {
    if (emailError.textContent) {
        hideError(emailError);
        this.style.borderColor = '#e1e5e9';
    }
});

passwordInput.addEventListener('input', function() {
    if (passwordError.textContent) {
        hideError(passwordError);
        this.style.borderColor = '#e1e5e9';
    }
});

// Form submission
loginForm.addEventListener('submit', function(e) {
    e.preventDefault();
    
    const email = emailInput.value.trim();
    const password = passwordInput.value;
    let isValid = true;
    
    // Reset previous errors
    hideError(emailError);
    hideError(passwordError);
    emailInput.style.borderColor = '#e1e5e9';
    passwordInput.style.borderColor = '#e1e5e9';
    
    // Validate email
    if (!email) {
        showError(emailError, 'Vui lòng nhập email');
        emailInput.style.borderColor = '#e74c3c';
        isValid = false;
    } else if (!validateEmail(email)) {
        showError(emailError, 'Vui lòng nhập email hợp lệ');
        emailInput.style.borderColor = '#e74c3c';
        isValid = false;
    }
    
    // Validate password
    if (!password) {
        showError(passwordError, 'Vui lòng nhập mật khẩu');
        passwordInput.style.borderColor = '#e74c3c';
        isValid = false;
    } else if (!validatePassword(password)) {
        showError(passwordError, 'Mật khẩu phải có ít nhất 6 ký tự');
        passwordInput.style.borderColor = '#e74c3c';
        isValid = false;
    }
    
    if (isValid) {
        // Show loading state
        loginBtn.classList.add('loading');
        loginBtn.disabled = true;
        
        // Simulate API call
        setTimeout(() => {
            // Hide loading state
            loginBtn.classList.remove('loading');
            loginBtn.disabled = false;
            
            // Check credentials (demo purposes)
            if (email === 'admin' && password === '123456') {
                // Success
                showSuccessMessage('Đăng nhập thành công!');
                
                // Save remember me preference
                if (rememberMeCheckbox.checked) {
                    localStorage.setItem('rememberMe', 'true');
                    localStorage.setItem('userEmail', email);
                } else {
                    localStorage.removeItem('rememberMe');
                    localStorage.removeItem('userEmail');
                }
                
                // Redirect after success (demo)
                setTimeout(() => {
                    alert('Chuyển hướng đến trang chủ...');
                }, 1500);
                
            } else {
                // Error
                showError(passwordError, 'Email hoặc mật khẩu không đúng');
                passwordInput.style.borderColor = '#e74c3c';
            }
        }, 2000); // Simulate 2 second delay
    }
});

// Success message function
function showSuccessMessage(message) {
    // Create success message element
    const successDiv = document.createElement('div');
    successDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #27ae60;
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 1000;
        animation: slideInRight 0.3s ease-out;
    `;
    successDiv.textContent = message;
    
    // Add animation keyframes
    if (!document.querySelector('#successAnimation')) {
        const style = document.createElement('style');
        style.id = 'successAnimation';
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `;
        document.head.appendChild(style);
    }
    
    document.body.appendChild(successDiv);
    
    // Remove after 3 seconds
    setTimeout(() => {
        successDiv.remove();
    }, 3000);
}

// Social login handlers
document.querySelector('.google-btn').addEventListener('click', function() {
    alert('Đăng nhập với Google (Demo)');
});

document.querySelector('.facebook-btn').addEventListener('click', function() {
    alert('Đăng nhập với Facebook (Demo)');
});

// Load saved email if remember me was checked
window.addEventListener('load', function() {
    if (localStorage.getItem('rememberMe') === 'true') {
        const savedEmail = localStorage.getItem('userEmail');
        if (savedEmail) {
            emailInput.value = savedEmail;
            rememberMeCheckbox.checked = true;
        }
    }
});

// Add smooth focus effects
const inputs = [emailInput, passwordInput];
inputs.forEach(input => {
    input.addEventListener('focus', function() {
        this.parentElement.style.transform = 'scale(1.02)';
        this.parentElement.style.transition = 'transform 0.2s ease';
    });
    
    input.addEventListener('blur', function() {
        this.parentElement.style.transform = 'scale(1)';
    });
});

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Enter key to submit form
    if (e.key === 'Enter' && (emailInput === document.activeElement || passwordInput === document.activeElement)) {
        loginForm.dispatchEvent(new Event('submit'));
    }
    
    // Escape key to clear form
    if (e.key === 'Escape') {
        emailInput.value = '';
        passwordInput.value = '';
        rememberMeCheckbox.checked = false;
        hideError(emailError);
        hideError(passwordError);
        emailInput.style.borderColor = '#e1e5e9';
        passwordInput.style.borderColor = '#e1e5e9';
    }
});
